import Constants from 'expo-constants';
import * as Device from 'expo-device';
import * as Notifications from 'expo-notifications';
import { StatusBar } from 'expo-status-bar';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { ActivityIndicator, BackHandler, Linking, Platform, StyleSheet, View, Text, TouchableOpacity } from 'react-native';
import Purchases from 'react-native-purchases';
import RevenueCatUI, { PAYWALL_RESULT } from "react-native-purchases-ui";
import { SafeAreaView } from 'react-native-safe-area-context';
import { WebView } from 'react-native-webview';
import BottomSheet, { BottomSheetView, BottomSheetBackdrop } from '@gorhom/bottom-sheet';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { AuthScreen } from '../components/auth/AuthScreen';
import { AuthProvider, useAuth } from '../contexts/AuthContext';

// adapty.activate('public_live_5jybKI12.BIvON8BGUevjdYFMrjVS');


// Configure notification handler
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldPlaySound: true,
    shouldSetBadge: true,
    shouldShowBanner: true,
    shouldShowList: true,
  }),
});

function handleRegistrationError(errorMessage: string) {
  console.error(errorMessage);
  throw new Error(errorMessage);
}

async function registerForPushNotificationsAsync() {
  if (Platform.OS === 'android') {
    Notifications.setNotificationChannelAsync('default', {
      name: 'default',
      importance: Notifications.AndroidImportance.MAX,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#FF231F7C',
    });
  }

  if (Device.isDevice) {
    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;
    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }
    if (finalStatus !== 'granted') {
      handleRegistrationError('Permission not granted to get push token for push notification!');
      return;
    }
    const projectId = Constants?.expoConfig?.extra?.eas?.projectId ?? Constants?.easConfig?.projectId;

    if (!projectId) {
      handleRegistrationError('Project ID not found');
    }
    try {
      const pushTokenString = (
        await Notifications.getExpoPushTokenAsync({
          projectId,
        })
      ).data;
      console.log('Push token:', pushTokenString);
      return pushTokenString;
    } catch (e: unknown) {
      handleRegistrationError(`${e}`);
    }
  } else {
    handleRegistrationError('Must use physical device for push notifications');
  }
}

function AppContent() {
  const { user, loading, session, storedCredentials, signOut } = useAuth();
  const [userAgent, setUserAgent] = useState<string>('');
  const [expoPushToken, setExpoPushToken] = useState('');
  const [canGoBack, setCanGoBack] = useState(false);
  const webViewRef = useRef<WebView>(null);

  // Bottom sheet for account deletion
  const bottomSheetRef = useRef<BottomSheet>(null);
  const [isBottomSheetOpen, setIsBottomSheetOpen] = useState(false);
  const snapPoints = useMemo(() => ['25%', '40%'], []);

  // Bottom sheet callbacks
  const handleSheetChanges = useCallback((index: number) => {
    setIsBottomSheetOpen(index >= 0);
  }, []);

  const openAccountDeleteSheet = useCallback(() => {
    bottomSheetRef.current?.expand();
  }, []);

  const closeAccountDeleteSheet = useCallback(() => {
    bottomSheetRef.current?.close();
  }, []);

  const handleContactSupport = useCallback(() => {
    Linking.openURL('mailto:<EMAIL>?subject=Account Deletion Request');
    closeAccountDeleteSheet();
  }, []);

  const handleOpenHelp = useCallback(() => {
    Linking.openURL('https://help.emergent.sh/');
    closeAccountDeleteSheet();
  }, []);

  // Custom backdrop component that blocks interactions
  const renderBackdrop = useCallback(
    (props: any) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
        pressBehavior="none"
      />
    ),
    []
  );

  // Configure RevenueCat Purchases
  useEffect(() => {
    Purchases.setLogLevel(Purchases.LOG_LEVEL.DEBUG);

    if (Platform.OS === 'ios') {
      Purchases.configure({ apiKey: 'appl_CJLOIaUNPTeauoGYqyZURTUlsfP' });
    } else if (Platform.OS === 'android') {
      Purchases.configure({ apiKey: '<public_google_api_key>' });

      // OR: if building for Amazon, be sure to follow the installation instructions then:
      // Purchases.configure({ apiKey: '<public_amazon_api_key>', useAmazon: true });
    }

    
  }, []);

  const showPaywall = async () => {
    const paywallResult: PAYWALL_RESULT = await RevenueCatUI.presentPaywall();
    console.log(paywallResult, 'result');
  
    // Check if payment was successful and reload WebView
    if (paywallResult === PAYWALL_RESULT.PURCHASED || paywallResult === PAYWALL_RESULT.RESTORED) {
      console.log('Payment successful, reloading WebView...');
      
      // Reload the WebView
      if (webViewRef.current) {
        webViewRef.current.reload();
      }
    } else if (paywallResult === PAYWALL_RESULT.CANCELLED) {
      console.log('Payment cancelled by user');
    } else if (paywallResult === PAYWALL_RESULT.ERROR) {
      console.log('Payment error occurred');
    }
  };

  useEffect(() => {
    if (user?.id) {
      Purchases.logIn(user.id);
    }
  }, [user?.id]);

  useEffect(() => {
    const generateUserAgent = () => {
      const deviceModel = Device.modelName || 'Unknown Device';
      const osVersion = Device.osVersion || '16.0';

      if (Platform.OS === 'ios') {
        return `Mozilla/5.0 (iPhone; CPU iPhone OS ${osVersion.replace('.', '_')} like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/${osVersion} Mobile/15E148 Safari/604.1`;
      } else {
        return `Mozilla/5.0 (Linux; Android ${osVersion}; ${deviceModel}) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36`;
      }
    };

    setUserAgent(generateUserAgent());
  }, []);

  // Handle hardware back button (Android) and enable back navigation
  useEffect(() => {
    const backAction = () => {
      if (canGoBack && webViewRef.current) {
        webViewRef.current.goBack();
        return true; // Prevent default behavior
      }
      return false; // Allow default behavior (exit app)
    };

    if (Platform.OS === 'android') {
      const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);
      return () => backHandler.remove();
    }
  }, [canGoBack]);

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar style="auto" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
        </View>
      </SafeAreaView>
    );
  }

  if (!user) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar style="auto" />
        <AuthScreen />
      </SafeAreaView>
    );
  }

  // Create the injected JavaScript with Supabase credentials
  // const supabaseHost = 'oakrnplafjvuupqxdokt.supabase.co';
  const authTokenKey = `sb-oakrnplafjvuupqxdokt-auth-token`;

  const injectedJavaScript = `
    // Store push token globally
    window.expoPushToken = "${expoPushToken}";

  ${session ? `
    window.localStorage.setItem("sb-oakrnplafjvuupqxdokt-auth-token", JSON.stringify({
      "access_token": "${session.access_token}",
      "expires_in": ${session.expires_in || 604800},
      "expires_at": ${session.expires_at},
      "refresh_token": "${session.refresh_token}",
      "token_type": "${session.token_type}",
      "user": ${JSON.stringify(user)}
    }));
  ` : ''}


    inspectLocalStorage();

    // Function to send push token to React app
    function sendPushTokenToReact() {
      if (window.expoPushToken) {
        localStorage.setItem('expoPushToken', window.expoPushToken);
        window.dispatchEvent(new CustomEvent('expoPushTokenReceived', {
          detail: { token: window.expoPushToken }
        }));
        console.log('Push token sent to React app:', window.expoPushToken);
      }
    }

    // Send token immediately when script loads
    sendPushTokenToReact();

    // Listen for requests from React app
    window.addEventListener('message', function(event) {
      if (event.data.type === 'GET_PUSH_TOKEN') {
        window.postMessage({type: 'PUSH_TOKEN', token: window.expoPushToken}, '*');
      }
    });

    // Listen for logout events from the web app
    window.addEventListener('logout', function(event) {
      console.log('Logout event received from web app');
      // Clear Supabase credentials from localStorage
      localStorage.removeItem('${authTokenKey}');
      // Notify the mobile app about the logout
      window.ReactNativeWebView.postMessage(JSON.stringify({
        type: 'LOGOUT_EVENT',
        timestamp: new Date().toISOString()
      }));
    });

    // Enable iOS back gesture and touch interactions
    document.addEventListener('DOMContentLoaded', function() {
      sendPushTokenToReact();
      document.body.style.webkitTouchCallout = 'none';
      document.body.style.webkitUserSelect = 'none';
      document.body.style.userSelect = 'none';
      document.body.style.webkitOverflowScrolling = 'touch';
      


      if (!document.querySelector('meta[name="viewport"]')) {
        var meta = document.createElement('meta');
        meta.name = 'viewport';
        meta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover';
        document.getElementsByTagName('head')[0].appendChild(meta);
      }
    });

    true; // Required for iOS
  `;

  return (
    <GestureHandlerRootView style={styles.container}>
      <SafeAreaView style={styles.container}>
        <StatusBar style="auto" />
        <WebView
        ref={webViewRef}
        source={{ uri: 'http://localhost:5173?mobile=true' }}
        style={styles.webview}
        userAgent={userAgent}
        javaScriptEnabled={true}
        domStorageEnabled={true}
        startInLoadingState={true}
        scalesPageToFit={true}
        injectedJavaScript={injectedJavaScript}
        sharedCookiesEnabled={true}
        thirdPartyCookiesEnabled={true}
        mixedContentMode="compatibility"
        cacheEnabled={true}
        allowsBackForwardNavigationGestures={true}
        bounces={true}
        scrollEnabled={true}
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        pullToRefreshEnabled={true}
        onNavigationStateChange={(navState) => {
          setCanGoBack(navState.canGoBack);
        }}
        onMessage={(event) => {
          try {
            const data = JSON.parse(event.nativeEvent.data);
            console.log('Message from WebView:', data);


            switch (data.type) {
              case 'REQUEST_PUSH_TOKEN':
                if (webViewRef.current && expoPushToken) {
                  webViewRef.current.postMessage(JSON.stringify({
                    type: 'PUSH_TOKEN_RESPONSE',
                    token: expoPushToken
                  }));
                }
                break;
              case 'OPEN_EXTERNAL_URL':
                Linking.openURL(data.url);
                break;
              case 'PUSH_TOKEN_RECEIVED':
                console.log('Web app confirmed push token receipt:', data.token);
                break;
              case 'TRIGGER_SUBSCRIPTION':
                showPaywall();
                break;
              case 'OPEN_MANAGE_SUBSCRIPTION':
                Linking.openURL('https://apps.apple.com/account/subscriptions');
                break;
              case 'OPEN_ACCOUNT_DELETE':
                openAccountDeleteSheet();
                break;
              case 'LOGOUT_EVENT':
                console.log('Logout event received from WebView at:', data.timestamp);
                // Call the mobile app's signOut method to clear local credentials and redirect to login
                signOut();
                break;
              default:
                console.log('Unknown message type:', data.type);
            }
          } catch (error) {
            console.error('Error parsing message from WebView:', error);
          }
        }}
        {...(Platform.OS === 'ios' && {
          allowsInlineMediaPlayback: true,
          mediaPlaybackRequiresUserAction: false,
          automaticallyAdjustContentInsets: false,
          contentInsetAdjustmentBehavior: 'never',
        })}
      />

      {/* Account Delete Bottom Sheet */}
      <BottomSheet
        ref={bottomSheetRef}
        index={-1}
        snapPoints={snapPoints}
        onChange={handleSheetChanges}
        enablePanDownToClose={false}
        enableOverDrag={false}
        enableContentPanningGesture={false}
        backdropComponent={renderBackdrop}
        backgroundStyle={styles.bottomSheetBackground}
        handleIndicatorStyle={styles.bottomSheetIndicator}
      >
        <BottomSheetView style={styles.bottomSheetContent}>
          <View style={styles.bottomSheetBody}>
            <View style={styles.bottomSheetHeader}>
              <Text style={styles.bottomSheetTitle}>Delete Account</Text>
              <TouchableOpacity onPress={closeAccountDeleteSheet} style={styles.closeButton}>
                <Text style={styles.closeButtonText}>✕</Text>
              </TouchableOpacity>
            </View>
            <Text style={styles.bottomSheetDescription}>
              To delete your account, please contact support
            </Text>
          </View>
          <View style={styles.buttonContainer}>
            <TouchableOpacity onPress={handleContactSupport} style={styles.contactSupportButton}>
              <Text style={styles.contactSupportButtonText}>Contact Support</Text>
            </TouchableOpacity>
            <TouchableOpacity onPress={handleOpenHelp} style={styles.helpButton}>
              <Text style={styles.helpButtonText}>Open Help</Text>
            </TouchableOpacity>
          </View>
        </BottomSheetView>
      </BottomSheet>
    </SafeAreaView>
    </GestureHandlerRootView>
  );
}
export default function RootLayout() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  webview: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  bottomSheetBackground: {
    backgroundColor: '#1a1a1a',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  bottomSheetIndicator: {
    backgroundColor: '#666',
  },
  bottomSheetContent: {
    flex: 1,
    padding: 20,
    height: '100%',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  bottomSheetBody: {
    flex: 1,
  },
  bottomSheetHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    marginBottom: 12,
  },
  bottomSheetTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
    textAlign: 'left'
  },
  closeButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  closeButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  bottomSheetDescription: {
    fontSize: 16,
    color: '#ccc',
    textAlign: 'left',
    lineHeight: 24,
    marginTop: 8,
  },
  buttonContainer: {
    gap: 12,
  },
  contactSupportButton: {
    backgroundColor: '#fff',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: 'center',
    width: '100%',
  },
  contactSupportButtonText: {
    color: '#000',
    fontSize: 16,
    fontWeight: '600',
  },
  helpButton: {
    backgroundColor: 'transparent',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: 'center',
    width: '100%',
    borderWidth: 1,
    borderColor: '#666',
  },
  helpButtonText: {
    color: '#ccc',
    fontSize: 16,
    fontWeight: '500',
  },
});
