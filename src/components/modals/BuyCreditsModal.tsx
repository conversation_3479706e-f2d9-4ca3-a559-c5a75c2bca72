import {
  Dialog,
  DialogContent,
  DialogTitle,
} from "../ui/dialog";
import { CheckIcon, HelpCircle, Loader2, X } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import WhiteCopperCoin from "@/assets/WhiteCopperCoin.svg";
import JoinedBuySVG from "@/assets/JoinedBuy.svg";
import StarsSVG from "@/assets/Stars.svg";
import { useCredits } from "@/contexts";
import { usePayment } from "@/hooks/use-payment";
import { useState } from "react";
import ExternalFilled from "@/assets/payments/external_filled.svg"
import DollarSign from "@/assets/payments/$.svg"
import InfoRed from "@/assets/payments/info-red.svg"

interface BuyCreditsModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

export function BuyCreditsModal({ isOpen, onOpenChange }: BuyCreditsModalProps) {
  const { tier } = useCredits();
  const {
    isLoading,
    processingBundleAmount,
    isUpgradeLoading,
    bundles,
    handleBuyCredits,
    handleUpgradeToPro
  } = usePayment();

  // Custom amount state
  const [customAmount, setCustomAmount] = useState<string>("");
  const [isCustomLoading, setIsCustomLoading] = useState(false);
  const [customAmountError, setCustomAmountError] = useState<string>("");

  // Calculate credits from custom amount (5 credits per $1)
  const calculateCredits = (amount: string): number => {
    const numAmount = parseInt(amount, 10);
    return isNaN(numAmount) ? 0 : numAmount * 5;
  };

  // Handle custom amount purchase
  const handleCustomBuy = async () => {
    const amount = parseInt(customAmount, 10);
    if (isNaN(amount) || amount < 1 || amount > 500) return;

    setIsCustomLoading(true);
    setCustomAmountError("");
    try {
      const customBundle = {
        amount: calculateCredits(customAmount),
        price: amount,
        selected: false
      };
      await handleBuyCredits(customBundle);
    } catch (error) {
      console.error("Failed to process custom purchase:", error);
      setCustomAmountError("Failed to process payment. Please try again.");
    } finally {
      setIsCustomLoading(false);
    }
  };

  // Format custom amount input
  const handleCustomAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;

    // Allow empty value
    if (value === "") {
      setCustomAmount("");
      setCustomAmountError("");
      return;
    }

    // Only allow integers, prevent decimal points and text input
    if (!/^\d+$/.test(value)) {
      return; // Don't update state if invalid format
    }

    const numValue = parseInt(value, 10);

    // Check for valid number
    if (isNaN(numValue)) {
      return;
    }

    // Allow input up to 99999 but don't allow values over that
    if (numValue > 99999) {
      return; // Don't update state if over 99999 limit
    }

    // Update the input value first
    setCustomAmount(value);

    // Set validation errors after updating the value
    if (numValue < 10) {
      setCustomAmountError("Minimum amount is $10");
    } else if (numValue > 500) {
      setCustomAmountError("Maximum amount is $500");
    } else {
      setCustomAmountError("");
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent
        className="w-[95%] sm:max-w-[640px] bg-[#18181A] text-white border border-[#242424] rounded-2xl p-8 pb-8 overflow-hidden"
        //@ts-ignore
        hideclosebutton
      >
        <div className="relative space-y-2 md:space-y-4">
          <div className="flex flex-col gap-2">
            <img
              src={JoinedBuySVG}
              alt="Add Credits"
              className="w-[60px] h-[60px]"
            />
            <div className="flex items-center gap-2">
              <DialogTitle className="text-[20px] md:text-[26px] font-medium text-white">
                {tier === "free" ? "Upgrade to Starter" : "Buy more credits"}
              </DialogTitle>
            </div>
            <button
              type="button"
              title="Close"
              onClick={() => onOpenChange(false)}
              className="absolute top-0 right-0 text-gray-400 hover:text-white"
            >
              <X size={20} />
            </button>
          </div>

          {/* Description */}
          {/* <div className="">
            <p className="text-[14px] md:text-[16px] text-white/50">
              Credits are our standard unit of measurement for emergent usage -
              the more complex or lengthy the task, the more credits it
              requires.
            </p>
          </div> */}

          {tier === "free" ? (
            // Free tier - Show upgrade option
            <div className="space-y-6">
              <div className="bg-[#212124] border border-[#FFFFFF1F] rounded-lg p-6">
                <div className="flex items-center gap-3 mb-4">
                  <img src={StarsSVG} alt="Pro" className="w-6 h-6" />
                  <h3 className="text-xl font-medium text-[#F3CA5F]">
                    Starter Tier Benefits
                  </h3>
                </div>
                <ul className="mb-6 space-y-3">
                  <li className="flex items-start gap-2">
                    <CheckIcon className="w-5 h-5 text-white mr-2 mt-0.5" />
                    <span className="text-white/80">50 credits per month</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckIcon className="w-5 h-5 text-white mr-2 mt-0.5" />
                    <span className="text-white/80">
                      Early access to new features
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckIcon className="w-5 h-5 text-white mr-2 mt-0.5" />
                    <span className="text-white/80">
                      Priority service during peak hours
                    </span>
                  </li>
                  {/* <li className="flex items-start gap-2">
                    <CheckIcon className="w-5 h-5 text-white mr-2 mt-0.5" />
                    <span className="text-white/80">
                      Ability to buy additional credits
                    </span>
                  </li> */}
                </ul>
                <button
                  type="button"
                  onClick={handleUpgradeToPro}
                  disabled={isUpgradeLoading}
                  className="w-full py-3 px-4 bg-[#F3CA5F] hover:bg-[#E7A93C] text-black font-semibold rounded-lg transition-colors flex items-center justify-center gap-2"
                >
                  {isUpgradeLoading ? (
                    <div className="flex items-center justify-center">
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      <span>Processing...</span>
                    </div>
                  ) : (
                    <>
                      <img src={StarsSVG} alt="Upgrade" className="w-5 h-5" />
                      <span>Upgrade to Starter - $10/month</span>
                    </>
                  )}
                </button>
              </div>
              <p className="text-sm text-white/50">
                You're currently on the Free tier with 5 monthly credits.
                Upgrade to Starter to buy additional credits.
              </p>
            </div>
          ) : (
            // Pro tier - Show buy credits option
            <>
              {/* Pricing info */}
              <div className="">
                <p className="text-sm text-white/50 md:text-[16px]">
                  <span className="font-medium">Get </span>
                  <span className="text-[#F3CA5F] font-medium">5 Credits</span>
                  <span className="font-medium"> for just </span>
                  <span className="text-[#29CC83]">$1</span>
                  <span className="font-medium">
                    ! Choose a bundle  or enter a custom amount and complete your payment.
                  </span>
                </p>
              </div>

              {/* Credit bundles */}
              <div className="grid gap-4 pt-4 md:grid-cols-3">
                {bundles.map((bundle) => (
                  <div
                    key={bundle.amount}
                    className="bg-[#212124] items-center group md:min-h-[208px] pt-4 md:pt-8 pb-4 px-4 border-[#FFFFFF1F] hover:border-[#FFFFFF4D] transition-all ease-in-out duration-100 border rounded-xl overflow-hidden"
                  >
                    <div className="flex items-center justify-between sm:gap-0 md:flex-col">
                      <div className="flex items-center gap-2 mb-2 md:flex-col">
                        <div>
                          <img
                            src={WhiteCopperCoin}
                            alt="Credits"
                            className="w-8 h-8 grayscale group-hover:grayscale-0"
                          />
                        </div>
                        <div className="md:mb-1 md:text-[24px] text-[#E6E6E6] font-bold">
                          {bundle.amount} credits
                        </div>
                      </div>
                      <div className="md:mb-6 md:text-[20px] text-[#E6E6E6]/40 group-hover:text-[#29CC83] transition-colors duration-200 ease-in-out">
                        ${bundle.price}
                      </div>
                      <button
                        type="button"
                        onClick={() => handleBuyCredits(bundle)}
                        disabled={isLoading}
                        className="w-fit md:w-full py-2 px-4 bg-[#cacacc] group-hover:bg-[#FFD566] duration-200 ease-in-out text-black font-semibold rounded-full tracking-[-0.2px] transition-colors"
                      >
                        {isLoading &&
                        processingBundleAmount === bundle.amount ? (
                          <div className="flex items-center justify-center">
                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                            <span>Processing...</span>
                          </div>
                        ) : (
                          "Buy Now"
                        )}
                      </button>
                    </div>
                  </div>
                ))}
              </div>

              {/* Custom amount input */}
              <div className="flex flex-col gap-2">
                <div className="bg-[#212124] border border-[#FFFFFF1F] rounded-xl p-[14px] py-[10px] pr-[10px] px-4">
                  <div className="flex items-center gap-3">
                    <div className="relative flex items-center flex-1 gap-1">
                      {customAmount && parseInt(customAmount) >= 0 && (
                        <img
                          src={DollarSign}
                          alt="Dollar"
                          className="w-5 h-5"
                        />
                      )}
                      <input
                        type="text"
                        value={customAmount}
                        onChange={handleCustomAmountChange}
                        placeholder=" + Enter custom amount ($)"
                        className="w-full text-lg pl-0 text-[#E6E6E6] font-['Inter'] text-[18px] font-semibold bg-transparent border-none outline-none placeholder:text-[#E6E6E6]/20 placeholder:p-0 placeholder:font-medium"
                        inputMode="numeric"
                        pattern="[0-9]*"
                      />
                    </div>
                    <button
                      type="button"
                      onClick={handleCustomBuy}
                      disabled={
                        !customAmount ||
                        parseInt(customAmount, 10) < 1 ||
                        parseInt(customAmount, 10) > 500 ||
                        isCustomLoading ||
                        !!customAmountError
                      }
                      className={`px-5 py-[6px] rounded-[8px] font-semibold transition-all duration-200 ease-out ${
                        customAmount &&
                        parseInt(customAmount, 10) >= 10 &&
                        parseInt(customAmount, 10) <= 500
                          ? "bg-[#F3CA5F] hover:bg-[#E7A93C] text-black"
                          : "bg-[#FFFFFF20] text-[#18181A] cursor-not-allowed"
                      }`}
                    >
                      {isCustomLoading ? (
                        <div className="flex items-center justify-center">
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          <span>Processing...</span>
                        </div>
                      ) : customAmount &&
                        parseInt(customAmount, 10) >= 1 &&
                        parseInt(customAmount, 10) <= 500 &&
                        !customAmountError ? (
                        `Buy ${calculateCredits(customAmount)} Credits`
                      ) : (
                        "Buy"
                      )}
                    </button>
                  </div>
                </div>
                <AnimatePresence mode="wait">
                  {customAmountError && (
                    <motion.div
                      initial={{ opacity: 0, x: 20, height: 0 }}
                      animate={{ opacity: 1, x: 0, height: "auto" }}
                      exit={{ opacity: 0, x: 20, height: 0 }}
                      transition={{
                        duration: 0.2,
                        ease: "easeOut",
                        height: { duration: 0.15 }
                      }}
                      className="text-[14px] font-['Inter'] font-medium text-[#E57373] flex items-center gap-[6px] overflow-hidden"
                    >
                      <img src={InfoRed} alt="Info" className="flex-shrink-0 w-5 h-5" />
                      <span>{customAmountError}</span>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </>
          )}

          {/* Learn more link */}
          <div className="pt-4">
            <button
              type="button"
              className="flex items-center text-sm text-white opacity-50 hover:text-white"
              onClick={() =>
                window.open("https://atlas-kb.com/atlas-e74243keac/articles/769724-credits-and-pricing", "_blank")
              }
            >
              <span className="font-['Inter'] text-[16px] font-medium">
                How do credits work?
              </span>
              <img
                src={ExternalFilled}
                alt="External"
                className="w-5 h-5 ml-1"
              />
            </button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
