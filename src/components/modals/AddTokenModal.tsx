import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
} from "../ui/dialog";
import { But<PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { useState } from "react";
import CoinIcon from "@/assets/copper-coin.svg";
import { useToast } from "@/hooks/use-toast";
import { agentApi } from "@/services/agentApi";
import { useAuth, useCredits } from "@/contexts";
import { CheckIcon, Loader2, X } from "lucide-react";
import RightArrowIcon from "@/assets/right-arrow.svg";
import StarsSVG from "@/assets/Stars.svg";
import AgentBudgetButton from "../AgentBudgetButton";
import AddCreditIcon from "@/assets/add_credits_modal.svg";
import WhiteCopperCoin from "@/assets/WhiteCopperCoin.svg";
import { ResponseImageData } from "@/types/message";
import { usePayment } from "@/hooks/use-payment";
import SparklingSVG from "@/assets/SparklingStar.svg"

interface AddTokenModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  currentCost: number;
  jobId: string;
  onBudgetUpdate?: (newBudget: number) => void;
  variant?: "increase_budget" | "add_credits"
  triggeredLocation?: "exit_cost" | "default" | "add_credits";
  modelName?: string;
  maxBudget?: number;
  handleSendMessage?: (content: string, images: ResponseImageData[]) => void;
}

export function AddTokenModal({
  isOpen,
  onOpenChange,
  currentCost,
  jobId,
  onBudgetUpdate,
  maxBudget = 20,
  modelName,
  triggeredLocation = "default",
  variant = "increase_budget",
  handleSendMessage,
}: AddTokenModalProps) {
  const [budgetIncrease, setBudgetIncrease] = useState(5.00);
  const [isLoading, setIsLoading] = useState(false);
  const [localVariant, setLocalVariant] = useState<"increase_budget" | "add_credits">(variant);
  // const [maxBudget, setMaxBudget] = useState(20);
  const { toast } = useToast();

  const handleIncrease = () => {
    setBudgetIncrease(prev => Math.min(prev + 1, 500));
  };

  const handleDecrease = () => {
    if(budgetIncrease > 1) {
      setBudgetIncrease(prev => Math.max(prev - 1, 1));
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;

    // Allow empty string for user to clear and type new value
    if (value === '') {
      setBudgetIncrease(0);
      return;
    }

    // Only allow numbers, decimal point, and prevent multiple decimal points
    const numericRegex = /^[0-9]*\.?[0-9]*$/;
    if (!numericRegex.test(value)) {
      return; // Don't update state if input contains non-numeric characters
    }

    // Allow partial decimal input (e.g., "5." or "5.0") but validate the numeric value
    if (value.endsWith('.') && !value.includes('..')) {
      // Allow typing decimal point, but don't update the numeric value yet
      return;
    }

    const numValue = parseFloat(value);

    // Only update if it's a valid number and within bounds (0 to 500)
    if (!isNaN(numValue) && numValue >= 0 && numValue <= 500) {
      // Round to 2 decimal places to match the display format
      setBudgetIncrease(Math.round(numValue * 100) / 100);
    } else if (numValue > 500) {
      // If user tries to enter more than 500, set it to 500
      setBudgetIncrease(500);
    }
  };

  const handleConfirm = async () => {
    // Validate budget increase value
    if (budgetIncrease <= 0) {
      toast({
        title: "Invalid Amount",
        description: "Budget increase must be greater than 0",
        variant: "destructive",
      });
      return;
    }

    if (budgetIncrease > 500) {
      toast({
        title: "Invalid Amount",
        description: "Budget increase cannot exceed 500 credits",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsLoading(true);
      // Update the budget
      await agentApi.updateBudget(jobId, budgetIncrease);

      // Fetch the latest budget info
      const budgetInfo = await agentApi.getBudget(jobId);

      // //console.log("Budget info:", budgetInfo);
      toast({
        title: "Budget Updated",
        description: `Successfully increased budget to ${budgetInfo.max_budget.toFixed(3)} Credits`,
      });

      if (onBudgetUpdate) {
        onBudgetUpdate(budgetInfo.max_budget);
      }

      onOpenChange(false);
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update budget",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const { tier, credits } = useCredits();

  // Use the payment hook for all payment-related functionality
  const {
    isLoading: paymentLoading,
    processingBundleAmount,
    isUpgradeLoading,
    bundles,
    handleBuyCredits,
    handleUpgradeToPro
  } = usePayment();

  const showDetails = (variant: string) => {
    if (variant === "increase_budget") {
      return (
        <>
          <DialogHeader className="border-b border-[#2E2F34] md:pb-6 p-4">
            <div className="flex items-center justify-between">
              <div className="flex flex-col items-start gap-2 md:gap-5">
                <div className="flex items-center justify-center w-10 h-10 rounded-full">
                  <img src={CoinIcon} alt="Coin" className="w-8 h-8" />
                </div>
                <div className="flex flex-col gap-1 md:gap-2">
                  <DialogTitle className="text-[#C4C4CC] font-brockmann text-[16px]  md:text-[22px] text-start font-medium md:leading-[28px] tracking-[-0.44px]">
                    Set Budget Limit
                  </DialogTitle>
                  <p className="text-[#5C5F66] font-inter text-[14px] text-start  md:text-base font-medium md:leading-6">
                    Session Budget prevent excessive token usage during a
                    session.
                  </p>
                </div>
              </div>
            </div>
          </DialogHeader>
          <div className="p-4 py-8 space-y-4 md:space-y-6 md:px-8 md:py-6">
            <div>
              <h3 className="text-[#8A8B91] text-[13px]  md:text-base md:mb-3">
                Budget Used
              </h3>
              <div className="text-[24px] md:text-[32px] font-semibold leading-[38px]">
                <span className="text-[#F3CA5F]">
                  {currentCost?.toFixed(3)}
                </span>
                <span className="text-[#C4C4CC] mx-2">/</span>
                <span className="text-[#C4C4CC]">{maxBudget?.toFixed(3)}</span>
                <span className="text-[#C4C4CC] ml-2 text-xl">Credits</span>
              </div>
            </div>

            <div>
              <h3 className="text-[#8A8B91] text-[13px]  md:text-base mb-3">
                Increase Budget
              </h3>
              <div className="flex items-center justify-between bg-[#0E0E0F] border border-[#242424] rounded-xl p-1 md:p-4">
                <button
                  type="button"
                  onClick={handleDecrease}
                  className="h-8 w-8 md:w-12 md:h-12 rounded-md md:rounded-xl bg-[#1E1F23] flex items-center justify-center text-2xl font-medium hover:bg-[#2A2B30] transition-colors"
                  disabled={isLoading}
                >
                  -
                </button>
                <div className="flex items-center">
                  <span className=" flex items-center md:text-[32px] font-semibold md:leading-[38px] mr-2">
                    <img
                      src={CoinIcon}
                      alt="Coin"
                      className="w-[18px] md:w-[28px] h-fit"
                    />
                    <Input
                      type="number"
                      value={
                        budgetIncrease === 0 ? "" : budgetIncrease.toString()
                      }
                      onChange={handleInputChange}
                      min="0"
                      max="500"
                      step="0.01"
                      className="text-[24px] md:text-[32px] font-semibold md:leading-[38px] bg-transparent border-none text-center p-0 focus:ring-0 focus:outline-none [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none [-moz-appearance:textfield]"
                      disabled={isLoading}
                      placeholder="0.00"
                    />
                  </span>
                </div>
                <button
                  type="button"
                  onClick={handleIncrease}
                  className=" h-8 w-8 md:w-12 md:h-12 rounded-md md:rounded-xl bg-[#1E1F23] flex items-center justify-center text-2xl font-medium hover:bg-[#2A2B30] transition-colors"
                  disabled={isLoading}
                >
                  +
                </button>
              </div>
            </div>

            {credits < budgetIncrease && (
                <div className="flex items-center justify-between">
                  <span className="text-[#F5CC62]/90 text-[13px]  md:text-base">
                    Not Enough Credits
                  </span>
                  <button title="Learn more about credits" className="bg-[#f5cc621a] text-[#F5CC62] py-[10px] px-3  rounded-xl" 
                  onClick={()=>{
                    setLocalVariant("add_credits");
                  }}> Buy Credits</button>
                </div>
             
            )}

            {credits > budgetIncrease && <div className="bg-[#f5cc621a] rounded-xl py-[10px] px-3">
              <div className="flex items-center justify-between">
                <span className="text-[#F5CC62] text-[13px]  md:text-base">
                  Your new budget limit
                </span>
                <div>
                  <span className="text-white/80 text-[13px]  md:text-base">
                    {currentCost?.toFixed(3)}
                  </span>
                  <span className="text-base text-white/90"> / </span>
                  <span className="text-[#F5CC62] text-[13px]  md:text-base">
                    {(maxBudget + budgetIncrease)?.toFixed(3)}
                  </span>
                  <span className="text-white/80 ml-2 text-[13px]  md:text-base">
                    Credits
                  </span>
                </div>
              </div>
            </div>}
          </div>

          <DialogFooter className="border-t border-[#2E2F34] p-4 md:mt-6 md:py-4">
            <div className="flex justify-end gap-3 md:px-6">
              <Button
                variant="outline"
                onClick={() => onOpenChange(false)}
                className="text-[#C4C4CC] px-4   h-[40px] hover:bg-[#242424] md:px-6 md:h-11 text-[16px]  md:text-base  font-medium"
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button
                onClick={handleConfirm}
                disabled={isLoading || budgetIncrease > credits}
                className="bg-white px-4 font-semibold   h-[40px] text-black hover:bg-[#E5E5E5] md:px-6 md:h-11 text-[16px]  md:text-base"
              >
                {isLoading ? "Updating..." : "Confirm"}
              </Button>
            </div>
          </DialogFooter>
        </>
      );
    } else if (variant === "add_credits") {
      return (
        <div className="relative p-4 space-y-4 md:p-8">
          <div className="flex flex-col gap-2">
            <img src={AddCreditIcon} alt="Add Credits" className="w-[40px] h-[40px] md:w-[60px] md:h-[60px]" />
            <div className="flex items-center gap-2">
              <DialogTitle className="text-[18px] md:text-[26px] font-medium text-white">
                {tier === "free" ? "Upgrade to Starter" : "Buy more credits"}
              </DialogTitle>
            </div>
            <button
              type="button"
              title="Close"
              onClick={() => onOpenChange(false)}
              className="absolute text-gray-400 top-4 right-4 hover:text-white"
            >
              <X size={20} />
            </button>
          </div>

          {tier === "free" ? (
            // Free tier - Show upgrade option
            <div className="space-y-4 md:space-y-6">
              <div className="bg-[#212124] border border-[#FFFFFF1F] rounded-lg px-4 pt-6 pb-4  md:p-6">
                <div className="flex items-center gap-1 mb-3 md:gap-3 md:mb-4">
                  <img src={SparklingSVG} alt="Pro" className="w-6 h-6" />
                  <h3 className="md:text-xl font-medium text-[#F3CA5F]">Starter Tier Benefits</h3>
                </div>
                <ul className="mb-6 space-y-3 md:mb-6 md:space-y-3">
                  <li className="flex items-start gap-2">
                  <CheckIcon className="w-5 h-5 text-white mr-2 mt-0.5" />
                    <span className="text-white/80">50 credits per month</span>
                  </li>
                  <li className="flex items-start gap-2">
                  <CheckIcon className="w-5 h-5 text-white mr-2 mt-0.5" />
                    <span className="text-white/80">Early access to new features</span>
                  </li>
                  <li className="flex items-start gap-2">
                  <CheckIcon className="w-5 h-5 text-white mr-2 mt-0.5" />
                    <span className="text-white/80">Priority service during peak hours</span>
                  </li>
                  {/* <li className="flex items-start gap-2">
                  <CheckIcon className="w-5 h-5 text-white mr-2 mt-0.5" />
                    <span className="text-white/80">Ability to buy additional credits</span>
                  </li> */}
                </ul>
                <button
                  type="button"
                  onClick={handleUpgradeToPro}
                  disabled={isUpgradeLoading}
                  className="w-full  p-3  md:py-3 md:px-4 bg-[#F3CA5F] hover:bg-[#E7A93C] text-black font-semibold rounded-lg transition-colors flex items-center justify-center gap-2"
                >
                  {isUpgradeLoading ? (
                    <div className="flex items-center justify-center">
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      <span>Processing...</span>
                    </div>
                  ) : (
                    <>
                      <img src={StarsSVG} alt="Upgrade" className="w-5 h-5" />
                      <span className="font-semibold text-[14px]  md:text-[16px]">Upgrade to Starter - $10/month</span>
                    </>
                  )}
                </button>
              </div>
              <p className="text-[12px] md:text-sm text-white/50">
                You're currently on the <span className="font-medium text-white">Free tier</span> with <span className="font-medium text-white">5 monthly credits</span>. Upgrade to Starter to buy additional credits.
              </p>
            </div>
          ) : (
            // Pro tier - Show buy credits option
            <>
              {/* Pricing info */}
              <div className="">
                <p className="text-white/50">
                  <span className="font-medium">Get </span>
                  <span className="text-[#F3CA5F] font-medium">5 Credits</span>
                  <span className="font-medium"> for just </span>
                  <span className="text-[#29CC83]">$1</span>
                  <span className="font-medium">! Choose a bundle and complete your payment.</span>
                </p>
              </div>

              {/* Credit bundles */}
              <div className="grid grid-cols-3 gap-4 py-4">
                {bundles.map((bundle) => (
                  <div
                    key={bundle.amount}
                    className="bg-[#212124] group min-h-[208px] pt-8 pb-4 px-4 border-[#FFFFFF1F] hover:border-[#FFFFFF4D] transition-all ease-in-out duration-100 border rounded-lg overflow-hidden"
                  >
                    <div className="flex flex-col items-center">
                      <div className="mb-2">
                        <img src={WhiteCopperCoin} alt="Credits" className="w-8 h-8 grayscale group-hover:grayscale-0" />
                      </div>
                      <div className="mb-1 text-[24px] text-[#E6E6E6] font-bold">{bundle.amount} credits</div>
                      <div className="mb-6 text-[20px] text-[#E6E6E6]/40 group-hover:text-[#29CC83] transition-colors duration-200 ease-in-out">${bundle.price}</div>
                      <button
                        type="button"
                        onClick={() => handleBuyCredits(bundle)}
                        disabled={paymentLoading}
                        className="w-full py-2 px-4 bg-[#cacacc] group-hover:bg-[#FFD566] duration-200 ease-in-out text-black font-semibold rounded-full tracking-[-0.2px] transition-colors"
                      >
                        {paymentLoading && processingBundleAmount === bundle.amount ? (
                          <div className="flex items-center justify-center">
                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                            <span>Processing...</span>
                          </div>
                        ) : (
                          "Buy Now"
                        )}
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </>
          )}
        </div>
      );
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[95%] sm:max-w-[650px] bg-[#0E0E0F] text-white border border-[#242424] rounded-2xl">
        {showDetails(localVariant)}
      </DialogContent>
    </Dialog>
  );
}
