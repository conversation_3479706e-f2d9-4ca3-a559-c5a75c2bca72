import {
  Dialog,
  DialogContent,
} from "../ui/dialog";
import { Loader2, X, CheckI<PERSON>, ChevronDown } from "lucide-react";
import { usePayment } from "@/hooks/use-payment";
import { useCredits } from "@/contexts";
import { formatDateOnly, formatMessageTimestamp } from "@/lib/utils/dateFormatter";
import { cn } from "@/lib/utils";

interface ManageSubscriptionModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

export function ManageSubscriptionModal({ isOpen, onOpenChange }: ManageSubscriptionModalProps) {
  // Use the payment hook for subscription management
  const { isUpgradeLoading: isEditBillingLoading, handleManageSubscription } = usePayment();
  const {tier, creditResponse } = useCredits();

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[300px] sm:max-w-[600px] min-w-[500px] w-fit bg-[#18181A] p-8 text-white border border-[#242424] rounded-2xl overflow-hidden">
        <div className="relative space-y-8">
          <div className="p-0 pb-4 border-none bg-[#18181A]">
            <div className="text-[26px] font-medium text-[#E6E6E6] flex flex-col">
             <span> Manage Your Subscriptions</span>
             <span className="text-[15px] font-['Inter'] font-medium text-[#FFFFFF99]">{creditResponse?.subscription.status == "initiated_cancellation" ? "Cancels on" : "Refreshes on"}  <span className="text-[#FFFFFF] font-['Inter'] font-medium">{formatDateOnly(creditResponse?.monthly_credits_refresh_date)}</span></span>
            </div>
            <button
              type="button"
              title="Close"
              onClick={() => onOpenChange(false)}
              className="absolute top-0 right-0 text-gray-400 hover:text-white"
            >
              <X size={24} />
            </button>
          </div>


          <div className="flex flex-col gap-6 md:flex-row">
            {/* Starter Tier */}

           {tier === "starter" &&  <div className="flex-1 bg-[#262629] rounded-xl p-6 pb-[6rem] flex flex-col relative border border-[#EBEBFF1F]  transition-all ease-in-out duration-200">
              <div className="absolute top-4 rounded-full right-4 bg-[#333333] text-xs font-medium px-2 py-1">
                BETA
              </div>
              <h3 className="text-xl font-medium mb-2 text-[#F3CA5F] flex items-center">
                Starter Tier <span className="ml-2">✨</span>
              </h3>
              <div className="flex items-baseline mb-6">
                <span className="text-4xl font-bold">$10</span>
                <span className="ml-2 text-gray-400">/ month</span>
              </div>

              <button
                type="button"
                className="w-full mb-6 p-[10px] pr-[16px] rounded-[8px] font-brockmann font-semibold text-base bg-gradient-to-r bg-[#474032] text-[#F3CA5F] tracking-[-0.2px]"
              >
                Current Active Plan
              </button>

              <div className="flex-grow space-y-4 ">
                <div className="flex items-start">
                  <CheckIcon className="w-5 h-5 text-[#E6E6E6] mr-2 mt-0.5" />
                  <span className="text-base">
                    <span className="font-medium">50 credits</span>
                    <span className="text-gray-400"> per month</span>
                  </span>
                </div>
                <div className="flex items-start">
                  <CheckIcon className="w-5 h-5 text-[#E6E6E6] mr-2 mt-0.5" />
                  <span className="text-base">
                    <span className="text-gray-400">Early access to new features</span>
                  </span>
                </div>
                <div className="flex items-start">
                  <CheckIcon className="w-5 h-5 text-[#E6E6E6] mr-2 mt-0.5" />
                  <span className="text-base">
                    <span className="text-gray-400">Priority service during peak hours</span>
                  </span>
                </div>

                {/* <div className="flex items-start">
                  <CheckIcon className="w-5 h-5 text-[#E6E6E6] mr-2 mt-0.5" />
                  <span className="text-base">
                    <span className="text-gray-400">Ability to buy additional credits</span>
                  </span>
                </div> */}
                
              </div>
            </div>}

            {/* Pro Tier */}
          {tier === "pro" &&   <div className="flex-1 bg-[#262629] rounded-xl p-6 pb-[6rem] flex flex-col relative border border-[#EBEBFF1F]  transition-all ease-in-out duration-200">
              <div className="absolute top-4 rounded-full right-4 bg-[#333333] text-xs font-medium px-2 py-1">
                BETA
              </div>
              <h3 className="text-xl font-medium mb-2 text-[#F3CA5F] flex items-center">
                Pro Tier <span className="ml-2">✨</span>
              </h3>
              <div className="flex items-baseline mb-6">
                <span className="text-4xl font-bold">$20</span>
                <span className="ml-2 text-gray-400">/ month</span>
              </div>

              <button
                type="button"
                className="w-full mb-6 p-[10px] pr-[16px] rounded-[8px] font-brockmann font-semibold text-base bg-gradient-to-r bg-[#474032] text-[#F3CA5F] tracking-[-0.2px]"
              >
                Current Active Plan
              </button>

              <div className="flex-grow space-y-4 ">
                <div className="flex items-start">
                  <CheckIcon className="w-5 h-5 text-[#E6E6E6] mr-2 mt-0.5" />
                  <span className="text-base">
                    <span className="font-medium">100 credits</span>
                    <span className="text-gray-400"> per month</span>
                  </span>
                </div>
                <div className="flex items-start">
                  <CheckIcon className="w-5 h-5 text-[#E6E6E6] mr-2 mt-0.5" />
                  <span className="text-base">
                    <span className="text-gray-400">Early access to new features</span>
                  </span>
                </div>
                <div className="flex items-start">
                  <CheckIcon className="w-5 h-5 text-[#E6E6E6] mr-2 mt-0.5" />
                  <span className="text-base">
                    <span className="text-gray-400">Priority service during peak hours</span>
                  </span>
                </div>

                {/* <div className="flex items-start">
                  <CheckIcon className="w-5 h-5 text-[#E6E6E6] mr-2 mt-0.5" />
                  <span className="text-base">
                    <span className="text-gray-400">Ability to buy additional credits</span>
                  </span>
                </div> */}
                
              </div>
            </div>}
          </div>

          <div className="flex items-center justify-between w-full bg-[#18181A] border-none gap-8">
            <div className="text-sm text-gray-400">
              Contact us at <a href="mailto:<EMAIL>" className="text-white underline"><EMAIL></a>
            </div>
            <div
              
              className={cn(`flex items-center cursor-pointer group ${isEditBillingLoading ? 'pointer-events-none opacity-70' : ''} `)}
              onClick={handleManageSubscription}
            >
              {isEditBillingLoading ? (
                <div className="flex items-center text-white/50 text-[16px] font-['Inter']">
                  <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                  <span>Processing...</span>
                </div>
              ) : (
                <>
                  <span className="text-white/50 group-hover:text-white text-[16px] font-['Inter'] flex items-center">{creditResponse?.subscription.status == "initiated_cancellation" ? "Resume" : "Cancel"} Subscription </span>
                  <ChevronDown className="w-4 h-4 ml-1 -rotate-90 opacity-50 group-hover:opacity-100" />
                </>
              )}
            </div>
             </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
