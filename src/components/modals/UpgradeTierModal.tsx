import {
  Dialog,
  DialogContent,
} from "../ui/dialog";
import { Loader2, X, CheckIcon } from "lucide-react";
import { usePayment } from "@/hooks/use-payment";

interface UpgradeTierModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

export function UpgradeTierModal({ isOpen, onOpenChange }: UpgradeTierModalProps) {
  // Use the payment hook for upgrade functionality
  const { isUpgradeLoading: isLoading, handleUpgradeToPro: handleUpgradeClick } = usePayment();

    const buttonStyle = {
    background: `
      linear-gradient(90deg, #ffffff30, #00000050),
      #F3CA5F
    `,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    backgroundBlendMode: 'overlay, normal',
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[90vw] md:max-w-[750px] w-full bg-[#18181A] p-4 md:p-8 text-white border border-[#242424] rounded-2xl overflow-hidden">
        <div className="relative space-y-4 md:space-y-8">
          <div className="p-0 pb-2 md:pb-4 border-none bg-[#18181A]">
            <div className="text-[20px] md:text-[26px] font-medium text-[#E6E6E6]">
              Upgrade your tier for more credits
            </div>
            <button
              type="button"
              title="Close"
              onClick={() => onOpenChange(false)}
              className="absolute top-0 right-0 text-gray-400 hover:text-white"
            >
              <X size={24} />
            </button>
          </div>


          <div className="flex flex-col gap-2 md:gap-6 md:flex-row">
            {/* Free Tier */}
            <div className="flex-1 bg-[#262629] md:min-h-[400px] rounded-xl p-3 md:p-6 flex flex-col  border  transition-colors duration-200">
              <h3 className="mb-2 text-[16px] font-semibold text-[#E6E6E6]">Free Tier</h3>
              <div className="flex items-baseline mb-6">
                <span className="text-[24px] md:text-[32px] text-[#E6E6E6] font-brockmann font-bold ">$0</span>
                <span className="ml-2 text-gray-400">/ month</span>
              </div>

              <button
                type="button"
                className="w-full mb-2 md:mb-6 p-[10px]  pr-[16px] text-base font-semibold rounded-[8px] items-center flex justify-center bg-white/15 text-white border-[#3A3A3B] hover:bg-[#3A3A3B]"
              >
                Current Active Plan
              </button>

              <div className="flex-grow space-y-4">
                <div className="flex items-start">
                  <CheckIcon className="w-5 h-5 text-white mr-2 mt-0.5" />
                  <span className="text-base">
                    <span className="font-medium">5 credits</span>
                    <span className="text-gray-400"> per month</span>
                  </span>
                </div>
                <div className="flex items-start">
                  <CheckIcon className="w-5 h-5 text-white mr-2 mt-0.5" />
                  <span className="text-base">
                    <span className="text-gray-400">Standard access</span>
                  </span>
                </div>
              </div>
            </div>

            {/* Pro Tier */}
            <div className="flex-1 bg-[#262629] rounded-xl p-3 md:p-6 flex flex-col relative border  transition-colors duration-200">
              <div className="absolute top-4 rounded-full right-4 bg-[#333333] text-xs font-medium px-2 py-1">
              </div>
              <h3 className="text-xl font-medium mb-2 text-[#F3CA5F] flex items-center">
                Starter Tier <span className="ml-2">✨</span>
              </h3>
              <div className="flex items-baseline mb-6">
                <span className="text-[30px] font-bold md:text-4xl">$10</span>
                <span className="ml-2 text-gray-400">/ month</span>
              </div>

              <button
                type="button"
                style={buttonStyle}
                className="w-full mb-2 md:mb-6 p-[10px] pr-[16px] rounded-[8px] font-brockmann font-semibold text-base  text-black  tracking-[-0.2px]"
                onClick={handleUpgradeClick}
                disabled={isLoading}
              >
                {isLoading ? (
                  <div className="flex items-center">
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    <span>Processing...</span>
                  </div>
                ) : (
                  "Upgrade to Starter"
                )}
              </button>

              <div className="flex-grow space-y-2 md:space-y-4 ">
                <div className="flex items-start">
                  <CheckIcon className="w-5 h-5 text-[#E6E6E6] mr-2 mt-0.5" />
                  <span className="text-base">
                    <span className="font-medium">50 credits</span>
                    <span className="text-gray-400"> per month</span>
                  </span>
                </div>
                <div className="flex items-start">
                  <CheckIcon className="w-5 h-5 text-[#E6E6E6] mr-2 mt-0.5" />
                  <span className="text-base">
                    <span className="text-gray-400">Early access to new features</span>
                  </span>
                </div>
                <div className="flex items-start">
                  <CheckIcon className="w-5 h-5 text-[#E6E6E6] mr-2 mt-0.5" />
                  <span className="text-base">
                    <span className="text-gray-400">Priority service during peak hours</span>
                  </span>
                </div>
                {/* <div className="flex items-start">
                  <CheckIcon className="w-5 h-5 text-[#E6E6E6] mr-2 mt-0.5" />
                  <span className="text-base">
                    <span className="text-gray-400">Ability to buy additional credits</span>
                  </span>
                </div> */}
              </div>
            </div>
          </div>

          <div className="flex items-center justify-start w-full bg-[#18181A] border-none">
            <div className="text-sm text-gray-400">
              Need help? Contact us at <a href="mailto:<EMAIL>" className="text-white underline"><EMAIL></a>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
