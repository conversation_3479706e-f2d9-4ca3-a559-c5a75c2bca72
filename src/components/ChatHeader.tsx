import { But<PERSON> } from "@/components/ui/button";
import CodeIcon from "@/assets/code.svg";
import CodeHoverIcon from "@/assets/code-hover.svg";
import DiffIcon from "@/assets/diff.svg";
import InfoIcon from "@/assets/info.svg";
import PreviewIcon from "@/assets/eye.svg";
import HistoryIcon from "@/assets/history.svg";

import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useState, useEffect, useMemo } from "react";
import { ChatHistoryModal } from "./modals/ChatHistoryModal";
import { useIsEmergentUser } from "@/hooks/useIsEmergentUser";
import { useDeploy } from "@/hooks/useDeploy";
import { Loader2 } from "lucide-react";
// @ts-ignore
import animatedSpinner from "../assets/animated-spinner.gif";
import { useTabState } from "./TabBar";
import DeployCloud from "@/assets/DeployCloud.svg";
import { cn } from "@/lib/utils";
import PulseDot from "./PulseDot";
import Redeploy from "@/assets/RedeployNew.svg"

interface ChatHeaderProps {
  handleVSCodeLink: () => void;
  handleShowDiff: () => void;
  handlePreviewClick: () => void;
  handleInfoClick: () => void;
  handleDeployClick?: () => void;
  showActions: boolean;
  hideImportantActions: boolean;
  jobId?: string;
  podIsPaused?: boolean;
  bulkWriteExists?: boolean;
  enableDeploy?: boolean;
  buildMode:  "brainstorming_requested" | "brainstorming_done" | "build" | null;
}

export const ChatHeader = ({
  handleVSCodeLink,
  handleShowDiff,
  handlePreviewClick,
  handleInfoClick,
  handleDeployClick,
  showActions,
  hideImportantActions,
  jobId,
  podIsPaused = false,
  bulkWriteExists,
  enableDeploy = true,
  buildMode,
}: ChatHeaderProps) => {
  const [isChatHistoryModalOpen, setIsChatHistoryModalOpen] = useState(false);
  const isEmergentUser = useIsEmergentUser();
  const {
    deployStatus,
    latestRunStatus,
    loading,
    checkDeployStatus,
    loadDeploymentHistory,
  } = useDeploy(jobId);

  const { getTabByJobId } = useTabState();

  const isShowCase = getTabByJobId(jobId || "")?.state?.showCase || false;

  // Memoize the tooltip style to prevent re-renders
  const tooltipStyle = useMemo(() => ({
    fontFamily: "Inter",
    fontWeight: 500
  }), []);

  useEffect(() => {
    if (jobId && !isShowCase && !podIsPaused && buildMode == "build") {
      checkDeployStatus(jobId, true);

      if (deployStatus !== "not_deployed") {
        loadDeploymentHistory();
      }
    }
  }, [jobId, isShowCase, podIsPaused, checkDeployStatus, deployStatus, loadDeploymentHistory, buildMode]);

  const deployButtonText = () => {
    if (loading) {
      return "Loading...";
    } else if (deployStatus === "running" || latestRunStatus === "running") {
      return "Deploying...";
    } else if (deployStatus === "success") {
      return "Redeploy";
    } else {
      return "Deploy";
    }
  };

  return (
    <>
      <div className="flex items-center gap-4">
        <div className="flex-1" />

        <div className="flex items-center">
          {showActions && !hideImportantActions && (
            <div className="flex items-center gap-1">
              <TooltipProvider>
                <div className="flex items-center gap-1">
                  {isEmergentUser && jobId && (
                    <Tooltip delayDuration={100}>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          className="group h-[36px] w-[36px] p-0 hover:bg-white/5 rounded-lg transition-colors duration-200"
                          onClick={() => setIsChatHistoryModalOpen(true)}
                        >
                          <div className="flex items-center justify-center w-full">
                            <img
                              src={HistoryIcon}
                              alt="chat history"
                              className="w-6 h-6 opacity-50 group-hover:opacity-100"
                            />
                          </div>
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent
                        side="bottom"
                        className="bg-[#DDDDE6] text-black border-0"
                        style={tooltipStyle}
                      >
                        Edit Chat History
                      </TooltipContent>
                    </Tooltip>
                  )}

                  <Tooltip delayDuration={100}>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        className="group h-[36px] w-[36px] p-0 bg-white/5 hover:bg-[#ffffff08] backdrop-blur-lg rounded-lg transition-colors duration-200"
                        onClick={handleInfoClick}
                      >
                        <div className="flex items-center justify-center w-full">
                          <img
                            src={InfoIcon}
                            alt="info"
                            className="w-6 h-6 opacity-50 group-hover:opacity-100"
                          />
                        </div>
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent
                      side="bottom"
                      className="bg-[#DDDDE6] text-black border-0"
                      style={tooltipStyle}
                    >
                      Info
                    </TooltipContent>
                  </Tooltip>
                </div>
              </TooltipProvider>
            </div>
          )}

          {showActions && (
            <>
               <div className="">
                 <Button
                    variant="default"
                    className="group h-[36px] ml-2 pl-2 pr-2 md:pr-3 rounded-[8px] hover:bg-white/100 transition-colors duration-200"
                    onClick={handleVSCodeLink}
                    disabled={podIsPaused || buildMode != "build"}
                  >
                    <div className="flex items-center gap-1">
                      <img
                        alt="code"
                        src={CodeIcon}
                        className="w-6 h-6"
                      />
                      <span className="hidden text-base font-semibold md:block">
                        Code
                      </span>
                    </div>
                  </Button>
               </div>

              <div className="ml-2">
                <Button
                  variant="default"
                  className="group h-[36px] pl-2 pr-2 md:pr-3 rounded-[8px] hover:bg-white/100 transition-colors duration-200"
                  onClick={handlePreviewClick}
                  disabled={buildMode != "build"}
                >
                  <div className="flex items-center gap-1">
                    <img alt="preview" src={PreviewIcon} className="w-6 h-6" />
                    <span className="hidden text-base font-semibold md:block">
                      Preview
                    </span>
                  </div>
                </Button>
              </div>

              {jobId &&
                handleDeployClick &&
                !hideImportantActions &&
                !podIsPaused &&
                !isShowCase && (
                  <div className="ml-2">
                    <Button
                      variant="ghost"
                      className={cn(
                        `group h-[36px] pl-2 pr-2 md:pr-3  rounded-[8px] hover:bg-white/10 transition-colors duration-200`,
                        deployStatus === "success"
                          ? "bg-white/10 grayscale-0"
                          : deployStatus === "running" ||
                            latestRunStatus === "running"
                          ? "bg-[#4DA5FF29] text-[#33DDFF]"
                          : "bg-gradient-to-r from-[#80FFF9] to-[#1588FC] text-[#0F0F10] hover:text-[#0F0F10] hover:opacity-80",
                        (loading || !enableDeploy) &&
                          deployStatus == "not_deployed" &&
                          "opacity-90 grayscale"
                      )}
                      onClick={handleDeployClick}
                      disabled={
                        (loading || !enableDeploy) &&
                        deployStatus == "not_deployed"
                      }
                    >
                      <div className="relative flex items-center gap-1">
                        {loading ? (
                          <Loader2 className="w-5 h-5 animate-spin" />
                        ) : deployStatus === "running" ||
                          latestRunStatus === "running" ? (
                          <img
                            src={animatedSpinner}
                            alt="deploying"
                            className="w-5 h-5"
                          />
                        ) : (
                         <div className="relative">
                          <img
                            src={deployStatus == "success" ? Redeploy : DeployCloud}
                            alt="Deploy"
                            className={cn("w-5 h-5 ", deployStatus != "success" && "invert")}
                          />
                          {deployStatus == "success" && <PulseDot
                            color="#2EE572"
                            size={16}
                            innerSize={8}
                            animate={deployStatus === "success"}
                            className="absolute -bottom-[4px] -right-[4.5px]"
                          />}
                         </div>
                        )}
                        <span className="hidden text-base font-semibold md:block">
                          {deployButtonText()}
                        </span>
                      </div>
                    </Button>
                  </div>
                )}
            </>
          )}
        </div>
      </div>

      <ChatHistoryModal
        isOpen={isChatHistoryModalOpen}
        onOpenChange={setIsChatHistoryModalOpen}
        chatId={jobId}
      />
    </>
  );
};
