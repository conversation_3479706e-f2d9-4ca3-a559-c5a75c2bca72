import { Popover, PopoverContent, PopoverTrigger } from "./ui/popover";
import { <PERSON><PERSON> } from "./ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "./ui/avatar";
import { MobileCreditsDisplay } from "./MobileCreditDisplay";
import MenuButton from "./ui/menu-button";

import GreenArrowLink from "@/assets/greenLinkArrow.svg";
import LogoutIcon from "@/assets/menu-icons/logout.svg";
import JoinDiscord from "@/assets/menu-icons/discord_hover.svg";
import NormalDiscord from "@/assets/menu-icons/discord.svg";
import DiscordEndIcon from "@/assets/menu-icons/discord_end_icon.svg";
import DeleteIcon from "@/assets/deployment/DustbinRed.svg";
import ConnectGitHubButton from "./github/ConnectGitHubButton";
import { Loader2 } from "lucide-react";
import { useGetGitHubUserDetailsQuery } from "@/store/api/apiSlice";
import GithubDot from "@/assets/github/git-dot.svg"  

import SupportHelp from "@/assets/menu-info.svg"
import SupportHelpHover from "@/assets/info-square-02-contained-filled.svg"
import { URL_LINKS } from "@/constants/constants";

interface MenuBarProps {
  user: any;
  isGitHubConnected: boolean;
  handleGitHubConfigClick: () => void;
  handleLogout: () => void;
  isLoggingOut: boolean;
  isOpen?: boolean;
  onOpenChange?: (open: boolean) => void;
}

function MenuBar({
  user,
  isGitHubConnected,
  handleGitHubConfigClick,
  handleLogout,
  isLoggingOut,
  isOpen,
  onOpenChange,
}: MenuBarProps) {
  const getUserInitials = () => {
    if (!user) return "U";
    return user?.email?.slice(0, 1).toUpperCase() || "U";
  };

      const {
        data: userDetails
      } = useGetGitHubUserDetailsQuery();
  return (
    <Popover open={isOpen} onOpenChange={onOpenChange}>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          className="relative w-8 h-8 p-0 rounded-full bg-[#1E1E1F] hover:bg-[#2A2A2B]"
        >
          <Avatar className="h-8 w-8 bg-[#1E1E1F]">
            <AvatarImage
              src={user?.user_metadata?.avatar_url}
              alt="User Avatar"
            />
            <AvatarFallback className="bg-[#1E1E1F] text-sm font-medium">
              {getUserInitials()}
            </AvatarFallback>
          </Avatar>
        </Button>
      </PopoverTrigger>
      <PopoverContent
        align="end"
        className="bg-[#131314] border-[#1f1f1f] mt-4 mb-2 ml-4 md:-mr-5 p-0  rounded-[12px] shadow-md"
      >
        <div className="hidden md:flex items-center gap-3 p-4 border-b border-[#1f1f1f]">
          <Avatar className="h-10 w-10 bg-[#2A2A2B] border-black/5 border-[4px]">
            <AvatarImage
              src={user?.user_metadata?.avatar_url}
              alt="User Avatar"
            />
            <AvatarFallback className="bg-[#2A2A2B] text-normal font-medium">
              {getUserInitials()}
            </AvatarFallback>
          </Avatar>
          <div className="flex flex-col overflow-hidden">
            <div className="text-normal font-medium truncate max-w-[180px]">
              {user?.user_metadata?.name}
            </div>
            <div className="text-[14px] text-gray-400 truncate max-w-[180px]">
              {user?.email}
            </div>
          </div>
        </div>

        <div className="md:hidden">
          <MobileCreditsDisplay />
        </div>

        {!isGitHubConnected && (
          <div className="flex flex-col p-[10px] border-b border-[#1f1f1f]">
            <ConnectGitHubButton />
          </div>
        )}

        {isGitHubConnected && (
          <div className="flex flex-col p-[10px] border-b border-[#1f1f1f]">
            <MenuButton
              hoveringEndIcon={GreenArrowLink}
              hoverIcon={GithubDot}
              icon={GithubDot}
              className="  w-full  border-[#1F1F1F] text-[#2EE572] hover:bg-[#2EE5720F] rounded-[10px]  hover:opacity-100"
              onClick={handleGitHubConfigClick}
            >
              {userDetails?.github?.account_name}
            </MenuButton>
          </div>
        )}

        <div className="flex flex-col pt-[10px] gap-1 p-[10px]">
          <MenuButton
            onClick={() => {
              window.open(URL_LINKS.helpCenter, "_blank");
            }}
            hoverIcon={SupportHelpHover}
            icon={SupportHelp}
            className="hover:text-opacity-100 hover:bg-[#80FFF90F]  w-full gap-3  hover:text-[#80FFF9] opacity-70 hover:opacity-100 transition-all ease-in-out  rounded-[8px]"
          >
            Help Center
          </MenuButton>
          <MenuButton
            onClick={() => {
              if (window?.ReactNativeWebView) {
                // Send message to React Native to open external browser
                window.ReactNativeWebView.postMessage(JSON.stringify({
                  type: 'OPEN_ACCOUNT_DELETE'
                }));
              }
            }}
            hoverIcon={SupportHelpHover}
            icon={DeleteIcon}
            className="hover:text-opacity-100 hover:bg-[#80FFF90F] w-full gap-3 hover:text-[#80FFF9] opacity-70 hover:opacity-100 transition-all ease-in-out rounded-[8px] text-red-300"
          >
            Delete Account
          </MenuButton>
          <MenuButton
            onClick={() => {
              window.open(URL_LINKS.socials.discord, "_blank");
            }}
            hoverIcon={JoinDiscord}
            icon={NormalDiscord}
            hoveringEndIcon={DiscordEndIcon}
            className=" hover:bg-[#808AFF0F]  w-full gap-3 opacity-70 hover:opacity-100 hover:text-[#808AFF] transition-all rounded-[8px]  ease-in-out  group/rotate "
          >
            Join Discord
          </MenuButton>
          <MenuButton
            onClick={handleLogout}
            hoverIcon={LogoutIcon}
            icon={LogoutIcon}
            loadingState={isLoggingOut}
            loadingNode={<Loader2 className="animate-spin" />}
            className=" text-[#CCCCCC] w-full gap-3 hover:bg-[#ffffff10]  hover:text-white opacity-70 hover:opacity-100 transition-all ease-in-out  rounded-[8px]  "
          >
            <div className="flex gap-2">
              {/* {isLoggingOut ? <Loader2 className="animate-spin" /> : ""} */}
              Logout
            </div>
          </MenuButton>
        </div>
      </PopoverContent>
    </Popover>
  );
}

export default MenuBar;
