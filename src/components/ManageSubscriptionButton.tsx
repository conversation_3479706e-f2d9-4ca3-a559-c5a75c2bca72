import { useState } from 'react';
import { Loader2 } from 'lucide-react';
import SettingsSVG from "@/assets/Settings.svg";
import { ManageSubscriptionModal } from './modals/ManageSubscriptionModal';
import { agentApi } from '@/services/agentApi';
import { useToast } from '@/hooks/use-toast';
import { usePayment } from '@/hooks/use-payment';

interface ManageSubscriptionButtonProps {
  className?: string;
}

export function ManageSubscriptionButton({
  className = ''
}: ManageSubscriptionButtonProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { handleManageSubscription } = usePayment();


  return (
    <>
      <button
        type="button"
        onClick={() => handleManageSubscription()}
        className={`flex items-center justify-between flex-1 px-4 py-3 space-x-1 font-medium text-center text-white transition-colors border rounded-lg cursor-pointer border-white/20 bg-white/5 hover:border-white/30 hover:bg-white/10 hover:text-white/80 ${className}`}
        title="Manage Subscriptions"
        disabled={isLoading}
      >
        {isLoading ? (
          <>
            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            <span>Loading...</span>
          </>
        ) : (
          <>
            Manage your Subscriptions
            <img
              src={SettingsSVG}
              alt="settings"
              className="w-6 h-6 ml-2"
            />
          </>
        )}
      </button>

      <ManageSubscriptionModal
        isOpen={isModalOpen}
        onOpenChange={setIsModalOpen}
      />
    </>
  );
}
