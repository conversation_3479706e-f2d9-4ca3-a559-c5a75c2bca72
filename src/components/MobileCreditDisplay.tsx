import { useState } from "react";
import { useCredits } from "@/contexts";
import { ChevronRight, Loader2, X } from "lucide-react";
import CopperCoin from "@/assets/copper-coin.svg";
import SparklingStar from "@/assets/SparklingStar.svg";
import StarsSVG from "@/assets/Stars.svg";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collpasible";
import { UpgradeTierModal } from "./modals/UpgradeTierModal";
import { usePayment } from "@/hooks/use-payment";
import { BuyCreditsModal } from "./modals/BuyCreditsModal";
import useIsMobileApp from "@/hooks/useIsMobileApp";

export function MobileCreditsDisplay() {
  const { credits, loading, error, tier } = useCredits();
  const [isOpen, setIsOpen] = useState(false);
  const [isUpgradeModalOpen, setIsUpgradeModalOpen] = useState(false);
  const [isBuyCreditsModalOpen, setIsBuyCreditsModalOpen] = useState(false);
  const isMobileApp = useIsMobileApp();
  const { handleManageSubscription } = usePayment();

  // Use the payment hook for all payment-related functionality
  const {
    isLoading,
    processingBundleAmount,
    isUpgradeLoading,
    bundles,
    handleBuyCredits,
    handleUpgradeToPro
  } = usePayment();

  if (loading) {
    return (
      <div className="w-full flex items-center gap-2 px-4 py-2 text-sm text-[#8A8B91] hover:bg-[#2A2A2B] transition-colors">
        <Loader2 className="h-4 w-4 mr-1 text-[#F3CA5F] animate-spin" />
        Loading credits...
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full flex items-center gap-2 px-4 py-2 text-sm text-destructive hover:bg-[#2A2A2B] transition-colors">
        Error loading credits
      </div>
    );
  }

  return (
    <div className="py-1 border-b border-[#2A2A2B]">
      {/* Credits display - Compact */}
      <div className="w-full px-3 py-1.5">
        <h2 className="text-xs font-medium text-white/50">Available Credits</h2>
        <div className="flex items-center space-x-2">
          <img alt="Balance Coin" src={CopperCoin} className="w-4 h-4" />
          <span className="text-[#F5CC62] text-base font-semibold">
            {credits.toLocaleString(undefined, {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            })}
          </span>
          <div className="flex justify-end w-full">
            {tier === "free" ? (
              <div className="px-2 py-0.5 rounded-full bg-[#363637] flex items-center justify-center text-[10px] font-medium">
                Free
              </div>
            ) : tier === "starter" ? (
              (
                <div className="px-2 py-0.5 rounded-full border border-[#37342A] bg-[#37342A] text-[#F3CA5F] flex items-center justify-center text-[10px] font-medium">
                  Starter
                  <img src={SparklingStar} alt="Pro" className="w-3 h-3 ml-0.5" />
                </div>
              )
            ) : (
              <div className="px-2 py-0.5 rounded-full border border-[#37342A] bg-[#37342A] text-[#F3CA5F] flex items-center justify-center text-[10px] font-medium">
                Pro
                <img src={SparklingStar} alt="Pro" className="w-3 h-3 ml-0.5" />
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Action section */}
      {tier === "free" && (
        // Free tier - Show compact upgrade option
        <div className="w-full px-3 py-2">
          <button
            type="button"
            onClick={handleUpgradeToPro}
            disabled={isUpgradeLoading}
            className="w-full bg-[#F3CA5F] text-black rounded-lg py-1.5 px-3 text-xs font-medium flex items-center justify-center gap-1"
          >
            {isUpgradeLoading ? (
              <div className="flex items-center gap-1">
                <Loader2 className="w-3 h-3 animate-spin" />
                <span>Processing...</span>
              </div>
            ) : (
              <>
                <img src={StarsSVG} alt="Upgrade" className="w-4 h-4" />
                <span>Upgrade to Starter</span>
              </>
            )}
          </button>
        </div>
      )}

      {tier !== "free" && isMobileApp && (
        // Pro/Starter tier - Show buy credits option
        <div
          className="w-full px-3 py-2"
          onClick={(e) => e.stopPropagation()}
        >
          <button
            type="button"
            onClick={(e) => handleManageSubscription()}
            // onClick={(e) => {
            //   e.preventDefault();
            //   e.stopPropagation();
            //   setTimeout(() => {
            //     setIsBuyCreditsModalOpen(true);
            //   }, 0);
            // }}
            className="flex w-[90%] mt-2 ml-3 bg-[#F3CA5F] text-black hover:bg-[#E7A93C] p-2 rounded-lg justify-between font-semibold tracking-[-0.2px] items-center gap-2"
          >
            Manage Subscription
            <span className="text-lg">+</span>
          </button>
        </div>
      )}

      {/* {isMobileApp && (
        // Pro/Starter tier - Show buy credits option
        <div
          className="w-full px-3 py-2"
          onClick={(e) => e.stopPropagation()}
        >
          <button
            type="button"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              if (window?.ReactNativeWebView) {
                // Send message to React Native to open external browser
                window.ReactNativeWebView.postMessage(JSON.stringify({
                  type: 'OPEN_EXTERNAL_URL',
                  url: 'https://app.emergent.sh'
                }));
              }
            }}
            className="flex w-[90%] mt-2 ml-3 bg-[#F3CA5F] text-black hover:bg-[#E7A93C] p-2 rounded-lg justify-between font-semibold tracking-[-0.2px] items-center gap-2"
          >
            Manage Account
            <span className="text-lg">+</span>
          </button>
        </div>
      )} */}

      <>
        <UpgradeTierModal
          isOpen={isUpgradeModalOpen}
          onOpenChange={setIsUpgradeModalOpen}
        />

        <BuyCreditsModal
          isOpen={isBuyCreditsModalOpen}
          onOpenChange={setIsBuyCreditsModalOpen}
        />
      </>


    </div>
  );
}
